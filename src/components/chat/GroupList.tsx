import { useEffect, useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext'
import { useChatStore } from '@/stores/chatStore'
import { PlusCircle, Users, ArrowUpDown, Clock, Star, MoreVertical, Trash2, Share2, Globe, Lock, UserPlus, MessageSquare, Copy } from 'lucide-react'
import { CreateGroup } from '@/components/chat/CreateGroup'
import { JoinGroup } from '@/components/chat/JoinGroup'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip"
import { UserProfileDialog } from "@/components/profile/UserProfileDialog"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Crown } from "lucide-react"
import { useToast } from '@/components/ui/use-toast'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Link } from 'react-router-dom'
import { getGroups, updateGroup, deleteGroup, leaveGroup, subscribeToUserGroups, getUserProfile, calculateUserStudyTime, calculateUserStudyStreak } from '@/utils/supabase'
import { Switch } from '@/components/ui/switch'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { MoreHorizontal } from "lucide-react"

interface UserProfile {
  id: string
  displayName: string
  username: string
  photoURL?: string
  email: string
  createdAt: string
  stats?: {
    totalStudyTime: number
    studyStreak: number
  }
}

type SortOption = 'latest' | 'oldest' | 'name' | 'members'

interface GroupListProps {
  onSelectGroup?: (groupId: string) => void;
}

export function GroupList({ onSelectGroup }: GroupListProps = {}) {
  const [isCreateOpen, setIsCreateOpen] = useState(false)
  const [isJoinOpen, setIsJoinOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [sortBy, setSortBy] = useState<SortOption>('latest')
  const [groupMembers, setGroupMembers] = useState<{ [groupId: string]: UserProfile[] }>({})
  const [deletingGroupId, setDeletingGroupId] = useState<string | null>(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [privacyDialogOpen, setPrivacyDialogOpen] = useState(false)
  const [selectedGroupId, setSelectedGroupId] = useState<string | null>(null)
  const { user } = useSupabaseAuth()
  const { groups, setGroups, removeGroup } = useChatStore()
  const { toast } = useToast()
  const [shareDialogOpen, setShareDialogOpen] = useState(false)
  const [selectedGroupForShare, setSelectedGroupForShare] = useState<any>(null)
  const [leavingGroupId, setLeavingGroupId] = useState<string | null>(null)
  const [leaveDialogOpen, setLeaveDialogOpen] = useState(false)

  useEffect(() => {
    if (!user) return

    const loadGroups = async () => {
      try {
        const userGroups = await getGroups(user.id)
        const formattedGroups = userGroups.map(group => ({
          id: group.id,
          name: group.name,
          description: group.description || '',
          members: group.members || [],
          createdAt: new Date(group.createdAt || '').getTime(),
          ownerId: group.owner_id || group.createdBy,
          inviteCode: group.inviteCode || '',
          isPublic: group.isPublic || false,
          lastMessage: group.last_message ? {
            id: (group.last_message as any)?.id || '',
            content: (group.last_message as any)?.content || '',
            senderId: (group.last_message as any)?.senderId || '',
            timestamp: (group.last_message as any)?.timestamp || Date.now(),
            groupId: group.id
          } : undefined
        }))

        console.log('Loaded groups:', formattedGroups)
        setGroups(formattedGroups)
        setIsLoading(false)
      } catch (error) {
        console.error('Error loading groups:', error)
        setIsLoading(false)
      }
    }

    loadGroups()

    // Set up real-time subscription
    const subscription = subscribeToUserGroups(user.id, (updatedGroups) => {
      const formattedGroups = updatedGroups.map(group => ({
        id: group.id,
        name: group.name,
        description: group.description || '',
        members: group.members || [],
        createdAt: new Date(group.createdAt || '').getTime(),
        ownerId: group.owner_id || group.createdBy,
        inviteCode: group.inviteCode || '',
        isPublic: group.isPublic || false,
        lastMessage: group.last_message ? {
          id: (group.last_message as any)?.id || '',
          content: (group.last_message as any)?.content || '',
          senderId: (group.last_message as any)?.senderId || '',
          timestamp: (group.last_message as any)?.timestamp || Date.now(),
          groupId: group.id
        } : undefined
      }))

      setGroups(formattedGroups)
    })

    return () => {
      subscription.unsubscribe()
    }
  }, [user, setGroups]);

  // Load member profiles for each group
  useEffect(() => {
    const loadGroupMembers = async () => {
      const membersData: { [groupId: string]: UserProfile[] } = {}

      for (const group of groups) {
        const memberProfiles = await Promise.all(
          group.members.map(async (memberId) => {
            try {
              const userProfile = await getUserProfile(memberId)
              if (userProfile) {
                // Calculate user stats from study sessions
                const totalStudyTime = await calculateUserStudyTime(memberId)
                const studyStreak = await calculateUserStudyStreak(memberId)

                return {
                  id: userProfile.id,
                  displayName: userProfile.display_name || userProfile.displayName || 'User',
                  username: userProfile.username || `user_${memberId.slice(0, 8)}`,
                  photoURL: userProfile.photo_url || userProfile.photoURL || undefined,
                  email: userProfile.email,
                  createdAt: userProfile.created_at || userProfile.member_since || '',
                  stats: {
                    totalStudyTime,
                    studyStreak
                  }
                } as UserProfile
              } else {
                // Fallback for users without profiles
                return {
                  id: memberId,
                  displayName: 'User',
                  username: `user_${memberId.slice(0, 8)}`,
                  photoURL: undefined,
                  email: '',
                  createdAt: '',
                  stats: {
                    totalStudyTime: 0,
                    studyStreak: 0
                  }
                } as UserProfile
              }
            } catch (error) {
              console.error('Error loading member profile:', error)
              return null
            }
          })
        )
        membersData[group.id] = memberProfiles.filter((profile): profile is UserProfile => profile !== null)
      }

      setGroupMembers(membersData)
    }

    if (groups.length > 0) {
      loadGroupMembers()
    }
  }, [groups])

  const sortedGroups = [...groups].sort((a, b) => {
    switch (sortBy) {
      case 'latest':
        return b.createdAt - a.createdAt
      case 'oldest':
        return a.createdAt - b.createdAt
      case 'name':
        return a.name.localeCompare(b.name)
      case 'members':
        return b.members.length - a.members.length
      default:
        return 0
    }
  })

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`
    }
    return `${minutes}m`
  }

  const handleDeleteGroup = async (groupId: string) => {
    if (!user) return

    setDeletingGroupId(groupId)
    try {
      // Delete the group from Supabase
      await deleteGroup(groupId)

      // Remove from local state
      removeGroup(groupId)

      toast({
        title: "Group deleted",
        description: "The group has been successfully deleted"
      })

      // Close the dialog and reset state
      setDeleteDialogOpen(false)
      setDeletingGroupId(null)
    } catch (error) {
      console.error('Error deleting group:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to delete group"
      })
      setDeletingGroupId(null)
    }
  }

  const handleInvite = async (group: any) => {
    // Generate invite link - using shorter format
    const inviteLink = `https://isotopeai.in/i/${group.inviteCode}`;
    
    // Create invite message
    const inviteMessage = `Join our study group on IsotopeAI!\n\n` +
      `Group: ${group.name}\n` +
      `Code: ${group.inviteCode}\n\n` +
      `Join directly: ${inviteLink}\n\n` +
      `✨ Let's study together!`;
    
    // Copy to clipboard
    try {
      await navigator.clipboard.writeText(inviteMessage);
      toast({
        title: "Invite copied!",
        description: "The invite message has been copied to your clipboard.",
      });
      
      // Show share options dialog
      setShareDialogOpen(true);
      setSelectedGroupForShare(group);
    } catch (error) {
      console.error('Error copying invite:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to copy invitation message"
      });
    }
  }

  const handlePrivacyToggle = (groupId: string) => {
    setSelectedGroupId(groupId)
    setPrivacyDialogOpen(true)
  }

  const updateGroupPrivacy = async (isPublic: boolean) => {
    if (!selectedGroupId || !user) return

    try {
      console.log(`Updating group ${selectedGroupId} privacy to isPublic:`, isPublic)
      
      // Make sure we're explicitly setting the isPublic field
      await updateGroup(selectedGroupId, { isPublic: isPublic })
      
      // Update local state with the correct isPublic value
      const updatedGroups = groups.map(group => 
        group.id === selectedGroupId 
          ? { ...group, isPublic: isPublic } 
          : group
      )
      setGroups(updatedGroups)
      
      toast({
        title: `Group is now ${isPublic ? 'public' : 'private'}`,
        description: isPublic 
          ? "Anyone can discover and join this group" 
          : "Only people with the invite code can join this group",
      })
      
      setPrivacyDialogOpen(false)
    } catch (error) {
      console.error('Error updating group privacy:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update group privacy settings"
      })
    }
  }

  const handleLeaveGroup = async (groupId: string) => {
    if (!user) return

    setLeavingGroupId(groupId)
    try {
      // Leave the group using Supabase function
      await leaveGroup(groupId, user.id)

      // Remove from local state
      removeGroup(groupId)

      toast({
        title: "Left group",
        description: "You have successfully left the group"
      })

      // Close the dialog and reset state
      setLeaveDialogOpen(false)
      setLeavingGroupId(null)
    } catch (error) {
      console.error('Error leaving group:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to leave group"
      })
      setLeavingGroupId(null)
    }
  }

  return (
    <>
      {/* Groups Display */}
      <div className="space-y-12">
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary" />
          </div>
        ) : sortedGroups.length === 0 ? (
          <div className="flex flex-col items-center justify-center min-h-[60vh] py-12 space-y-8">
            <div className="text-center space-y-3">
              <p className="text-lg text-white/80">
                No groups yet
              </p>
              <p className="text-base text-white/60">
                Create a group or join one to get started!
              </p>
            </div>
            <div className="flex gap-4">
              <Button
                variant="outline"
                onClick={() => setIsJoinOpen(true)}
                className="bg-white/5 hover:bg-white/10 border-white/10 px-8"
              >
                <Users className="h-4 w-4 mr-2" />
                Join Group
              </Button>
              <Button
                variant="outline"
                onClick={() => setIsCreateOpen(true)}
                className="bg-white/5 hover:bg-white/10 border-white/10 px-8"
              >
                <PlusCircle className="h-4 w-4 mr-2" />
                Create Group
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-12">
            {sortedGroups.map((group) => (
              // Theme-aware group container
              <div 
                key={group.id} 
                className="space-y-6 p-6 border border-border dark:border-white/10 rounded-xl bg-card dark:bg-white/5 hover:bg-accent dark:hover:bg-white/10 transition-colors cursor-pointer"
                onClick={() => onSelectGroup && onSelectGroup(group.id)}
              >
                {/* Group Header */}
                <div className="flex items-center justify-between">
                  <div>
                    <div className="flex items-center gap-2">
                      <h2 className="text-xl font-semibold">{group.name}</h2>
                      {group.isPublic ? (
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="text-xs bg-indigo-500/20 text-indigo-400 px-2 py-0.5 rounded flex items-center gap-1">
                              <Globe className="h-3 w-3" />
                              <span>Public</span>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Anyone can discover and join this group</p>
                          </TooltipContent>
                        </Tooltip>
                      ) : (
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="text-xs bg-slate-500/20 text-slate-400 px-2 py-0.5 rounded flex items-center gap-1">
                              <Lock className="h-3 w-3" />
                              <span>Private</span>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Only people with the invite code can join</p>
                          </TooltipContent>
                        </Tooltip>
                      )}
                    </div>
                    {/* Theme-aware text */}
                    {group.description && (
                      <p className="text-sm text-muted-foreground dark:text-white/80 mt-1">
                        {group.description}
                      </p>
                    )}
                    {/* Theme-aware text */}
                    <p className="text-sm text-muted-foreground dark:text-white/60 mt-1">
                      {group.members.length} members
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    {group.ownerId === user?.id && (
                      <span className="text-xs bg-purple-500/20 text-purple-400 px-2 py-0.5 rounded">
                        Owner
                      </span>
                    )}

                    {/* Allow all members to invite others */}
                    <Tooltip>
                      <TooltipTrigger asChild>
                        {/* Theme-aware button */}
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-muted-foreground dark:text-white/60 hover:text-primary dark:hover:text-purple-400 hover:bg-primary/10 dark:hover:bg-purple-500/10"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleInvite(group);
                          }}
                        >
                          <Share2 className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Invite Friends</p>
                      </TooltipContent>
                    </Tooltip>

                    {group.ownerId === user?.id && (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          {/* Theme-aware button */}
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-muted-foreground dark:text-white/60 hover:text-primary dark:hover:text-purple-400 hover:bg-primary/10 dark:hover:bg-purple-500/10"
                          >
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        {/* Theme-aware dropdown */}
                        <DropdownMenuContent align="end" className="bg-popover border-border dark:bg-slate-900 dark:border-white/10 text-popover-foreground dark:text-white">
                          <DropdownMenuItem
                            className="flex items-center gap-2 cursor-pointer hover:bg-accent dark:hover:bg-slate-800" // Added hover style
                            onClick={() => handlePrivacyToggle(group.id)}
                          >
                            {group.isPublic ? (
                              <>
                                <Lock className="h-4 w-4" />
                                <span>Make Private</span>
                              </>
                            ) : (
                              <>
                                <Globe className="h-4 w-4" />
                                <span>Make Public</span>
                              </>
                            )}
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="flex items-center gap-2 text-red-500 dark:text-red-400 cursor-pointer hover:bg-destructive/10 dark:hover:bg-red-900/50" // Added hover style
                            onClick={() => {
                              setDeletingGroupId(group.id);
                              setDeleteDialogOpen(true);
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                            <span>Delete Group</span>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}

                    {group.ownerId !== user?.id && (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          {/* Theme-aware button */}
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-muted-foreground dark:text-white/60 hover:text-primary dark:hover:text-purple-400 hover:bg-primary/10 dark:hover:bg-purple-500/10"
                          >
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        {/* Theme-aware dropdown */}
                        <DropdownMenuContent align="end" className="bg-popover border-border dark:bg-slate-900 dark:border-white/10 text-popover-foreground dark:text-white">
                          <DropdownMenuItem
                            className="flex items-center gap-2 text-red-500 dark:text-red-400 cursor-pointer hover:bg-destructive/10 dark:hover:bg-red-900/50" // Added hover style
                            onClick={() => {
                              setLeavingGroupId(group.id);
                              setLeaveDialogOpen(true);
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                            <span>Leave Group</span>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}
                  </div>
                </div>

                {/* Members Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {groupMembers[group.id]?.map((member) => (
                    <UserProfileDialog
                      key={member.id}
                      userId={member.id}
                      username={member.username || member.displayName}
                      photoURL={member.photoURL}
                    >
                      {/* Theme-aware member card */}
                      <div className="flex flex-col items-center p-6 rounded-xl border border-border dark:border-white/10 bg-card dark:bg-white/5 backdrop-blur-sm relative cursor-pointer hover:bg-accent dark:hover:bg-white/10 transition-colors">
                        {/* Profile Header */}
                        <div className="absolute top-3 right-3 flex items-center gap-2">
                          {group.ownerId === member.id && (
                            <Crown className="h-5 w-5 text-yellow-500" />
                          )}
                          {member.id === user?.id && (
                            <span className="text-xs bg-purple-500/20 text-purple-400 px-2 py-0.5 rounded">
                              You
                            </span>
                          )}
                        </div>

                        {/* Avatar */}
                        {/* Theme-aware border and fallback */}
                        <Avatar className="h-24 w-24 mb-4 border-2 border-border dark:border-white/10">
                          <AvatarImage src={member.photoURL} />
                          <AvatarFallback className="bg-muted dark:bg-white/5">
                            <Users className="h-12 w-12 text-muted-foreground dark:text-white/60" />
                          </AvatarFallback>
                        </Avatar>

                        {/* User Info */}
                        <div className="text-center space-y-1">
                          {/* Theme-aware text */}
                          <h3 className="font-semibold text-lg text-foreground dark:text-white">{member.username || 'Anonymous'}</h3>
                        </div>

                        {/* Stats Section */}
                        {/* Theme-aware border */}
                        <div className="w-full mt-6 pt-4 border-t border-border dark:border-white/10 space-y-2">
                          <div className="flex justify-between items-center text-sm">
                            {/* Theme-aware text */}
                            <span className="text-muted-foreground dark:text-white/60">Time Studied</span>
                            {/* Keep purple color for stat */}
                            <span className="font-medium text-purple-400">
                              {member.stats ? formatDuration(member.stats.totalStudyTime) : '0m'}
                            </span>
                          </div>
                          <div className="flex justify-between items-center text-sm">
                            {/* Theme-aware text */}
                            <span className="text-muted-foreground dark:text-white/60">Study Streak</span>
                            {/* Keep purple color for stat */}
                            <span className="font-medium text-purple-400">
                              {member.stats?.studyStreak || 0} days
                            </span>
                          </div>
                        </div>
                      </div>
                    </UserProfileDialog>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-end gap-3">
          <DropdownMenu>
            <Tooltip>
              <TooltipTrigger asChild>
                <DropdownMenuTrigger asChild>
                  {/* Theme-aware button */}
                  <Button variant="ghost" size="icon" className="bg-secondary/50 dark:bg-white/5 hover:bg-secondary dark:hover:bg-white/10 backdrop-blur-lg text-muted-foreground dark:text-white">
                    <ArrowUpDown className="h-5 w-5" />
                  </Button>
                </DropdownMenuTrigger>
              </TooltipTrigger>
              <TooltipContent>
                <p>Sort Groups</p>
              </TooltipContent>
            </Tooltip>
            {/* Theme-aware dropdown */}
            <DropdownMenuContent align="end" className="w-48 bg-popover border-border text-popover-foreground">
              <DropdownMenuItem onClick={() => setSortBy('latest')} className="hover:bg-accent">
                <Clock className="mr-2 h-4 w-4" />
                Latest First
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSortBy('oldest')}>
                <Clock className="mr-2 h-4 w-4" />
                Oldest First
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSortBy('name')} className="hover:bg-accent">
                <Star className="mr-2 h-4 w-4" />
                By Name
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSortBy('members')} className="hover:bg-accent">
                <Users className="mr-2 h-4 w-4" />
                Most Members
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <CreateGroup open={isCreateOpen} onOpenChange={setIsCreateOpen} />
      <JoinGroup open={isJoinOpen} onOpenChange={setIsJoinOpen} />

      {/* Delete Group Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        {/* Theme-aware dialog */}
        <AlertDialogContent className="bg-background dark:bg-slate-900 border-border dark:border-white/10 text-foreground dark:text-white">
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Group</AlertDialogTitle>
            <AlertDialogDescription className="text-muted-foreground">
              This action cannot be undone. This will permanently delete the group and all its messages.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            {/* Theme-aware cancel */}
            <AlertDialogCancel className="bg-transparent border-border dark:border-white/10 text-foreground dark:text-white hover:bg-accent dark:hover:bg-white/10 hover:text-accent-foreground dark:hover:text-white">Cancel</AlertDialogCancel>
            {/* Keep destructive action */}
            <AlertDialogAction
              className="bg-red-600 hover:bg-red-700 text-white"
              onClick={() => deletingGroupId && handleDeleteGroup(deletingGroupId)}
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Privacy Settings Dialog */}
      <Dialog open={privacyDialogOpen} onOpenChange={setPrivacyDialogOpen}>
        {/* Theme-aware dialog */}
        <DialogContent className="bg-background dark:bg-slate-900 border-border dark:border-white/10 text-foreground dark:text-white sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Group Privacy Settings</DialogTitle>
            <DialogDescription className="text-muted-foreground">
              Choose whether this group is public or private
            </DialogDescription>
          </DialogHeader>
          
          {selectedGroupId && (
            <div className="py-4 space-y-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Globe className="h-5 w-5 text-indigo-400" />
                  <div>
                    {/* Theme-aware text */}
                    <h4 className="font-medium text-foreground">Public Group</h4>
                    <p className="text-sm text-muted-foreground dark:text-white/60">Anyone can discover and join this group</p>
                  </div>
                </div>
                {/* Switch should adapt */}
                <Switch
                  checked={Boolean(groups.find(g => g.id === selectedGroupId)?.isPublic)}
                  onCheckedChange={(checked) => {
                    console.log('Setting group to public:', checked);
                    updateGroupPrivacy(checked);
                  }}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Lock className="h-5 w-5 text-muted-foreground dark:text-slate-400" />
                  <div>
                    {/* Theme-aware text */}
                    <h4 className="font-medium text-foreground">Private Group</h4>
                    <p className="text-sm text-muted-foreground dark:text-white/60">Only people with the invite code can join</p>
                  </div>
                </div>
                {/* Switch should adapt */}
                <Switch
                  checked={!Boolean(groups.find(g => g.id === selectedGroupId)?.isPublic)}
                  onCheckedChange={(checked) => {
                    console.log('Setting group to private:', checked);
                    updateGroupPrivacy(!checked);
                  }}
                />
              </div>
            </div>
          )}
          
          <DialogFooter>
            {/* Theme-aware button */}
            <Button variant="outline" onClick={() => setPrivacyDialogOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Share Dialog */}
      <Dialog open={shareDialogOpen} onOpenChange={setShareDialogOpen}>
        {/* Theme-aware dialog */}
        <DialogContent className="bg-background border-border text-foreground sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Share Group Invite</DialogTitle>
          </DialogHeader>
          {selectedGroupForShare && (
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label>Invite Link</Label>
                <div className="flex gap-2">
                  {/* Theme-aware input */}
                  <Input 
                    value={`https://isotopeai.in/i/${selectedGroupForShare.inviteCode}`} 
                    readOnly 
                    className="bg-muted border-border"
                  />
                  {/* Theme-aware button */}
                  <Button 
                    size="icon" 
                    variant="outline" 
                    onClick={async () => {
                      const link = `https://isotopeai.in/i/${selectedGroupForShare.inviteCode}`;
                      await navigator.clipboard.writeText(link);
                      toast({
                        title: "Copied!",
                        description: "Invite link copied to clipboard"
                      });
                    }}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
                <p className="text-sm text-muted-foreground">
                  Share this link for one-click joining
                </p>
              </div>
              
              <div className="grid gap-2">
                <Label>Invite Code</Label>
                <div className="flex gap-2">
                  {/* Theme-aware input */}
                  <Input 
                    value={selectedGroupForShare.inviteCode} 
                    readOnly 
                    className="bg-muted border-border"
                  />
                  {/* Theme-aware button */}
                  <Button 
                    size="icon" 
                    variant="outline" 
                    onClick={() => {
                      const message = `Join our study group "${selectedGroupForShare.name}" on IsotopeAI!\n\nCode: ${selectedGroupForShare.inviteCode}\nLink: https://isotopeai.in/i/${selectedGroupForShare.inviteCode}`;
                      navigator.clipboard.writeText(message);
                      toast({
                        title: "Copied!",
                        description: "Short invite message copied to clipboard"
                      });
                    }}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
                <p className="text-sm text-muted-foreground">
                  Share this code with others to let them join your group
                </p>
              </div>
              
              <div className="grid gap-2">
                <Label>Share Options</Label>
                <div className="grid grid-cols-2 gap-2">
                  {/* Theme-aware button */}
                  <Button 
                    variant="outline" 
                    className="flex items-center gap-2" 
                    onClick={() => {
                      const inviteMessage = `Join our study group on IsotopeAI!\n\n` +
                        `Group: ${selectedGroupForShare.name}\n` +
                        `Code: ${selectedGroupForShare.inviteCode}\n\n` +
                        `Join directly: https://isotopeai.in/i/${selectedGroupForShare.inviteCode}\n\n` +
                        `✨ Let's study together!`;
                        
                        navigator.clipboard.writeText(inviteMessage);
                        toast({
                          title: "Copied!",
                          description: "Invite message copied to clipboard"
                        });
                    }}
                  >
                    <Copy className="h-4 w-4" />
                    <span>Copy Invite</span>
                  </Button>
                  
                  {/* Theme-aware button */}
                  <Button 
                    variant="outline"
                    className="flex items-center gap-2" 
                    onClick={() => {
                      const inviteMessage = `Join our study group on IsotopeAI!\n\n` +
                        `Group: ${selectedGroupForShare.name}\n` +
                        `Code: ${selectedGroupForShare.inviteCode}\n\n` +
                        `Join directly: https://isotopeai.in/i/${selectedGroupForShare.inviteCode}\n\n` +
                        `✨ Let's study together!`;
                        
                        // Create WhatsApp share URL
                        const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(inviteMessage)}`;
                        
                        // Open WhatsApp share in new window
                        window.open(whatsappUrl, '_blank');
                    }}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-green-500">
                      <path d="M3 21l1.65-3.8a9 9 0 1 1 3.4 2.9L3 21" />
                      <path d="M9 10a.5.5 0 0 0 1 0V9a.5.5 0 0 0-1 0v1Z" />
                      <path d="M13.5 10a.5.5 0 0 0 1 0V9a.5.5 0 0 0-1 0v1Z" />
                      <path d="M9 13.5a.5.5 0 0 0 .5.5h5a.5.5 0 0 0 0-1h-5a.5.5 0 0 0-.5.5Z" />
                    </svg>
                    <span>Share via WhatsApp</span>
                  </Button>
                </div>
              </div>
              
              {/* Theme-aware button */}
              <Button variant="outline" onClick={() => setShareDialogOpen(false)}>Done</Button>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Leave Group Dialog */}
      <AlertDialog open={leaveDialogOpen} onOpenChange={setLeaveDialogOpen}>
        {/* Theme-aware dialog */}
        <AlertDialogContent className="bg-background dark:bg-[#1a1f3c] border-border dark:border-white/20 text-foreground dark:text-white">
          <AlertDialogHeader>
            <AlertDialogTitle>Leave Group?</AlertDialogTitle>
            <AlertDialogDescription className="text-muted-foreground dark:text-white/60">
              Are you sure you want to leave this group? You'll need an invite to rejoin.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            {/* Theme-aware cancel */}
            <AlertDialogCancel className="bg-transparent dark:bg-white/10 hover:bg-accent dark:hover:bg-white/20 border-border dark:border-0">
              Cancel
            </AlertDialogCancel>
            {/* Theme-aware action */}
            <AlertDialogAction
              onClick={() => leavingGroupId && handleLeaveGroup(leavingGroupId)}
              className="bg-destructive/10 dark:bg-red-500/20 text-destructive dark:text-red-400 hover:bg-destructive/20 dark:hover:bg-red-500/30 border-0"
              disabled={leavingGroupId === null}
            >
              {leavingGroupId ? (
                "Leave"
              ) : (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current" />
                  <span>Leaving...</span>
                </div>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}

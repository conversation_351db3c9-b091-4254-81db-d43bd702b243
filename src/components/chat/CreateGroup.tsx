import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext'
import { useChatStore } from '@/stores/chatStore'
import { createGroup, generateInviteCode } from '@/utils/supabase'
import { Copy, Share2, Globe, Lock } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'
import { Switch } from '@/components/ui/switch'

interface CreateGroupProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onCreateSuccess?: (groupId: string) => void
}

export function CreateGroup({ open, onOpenChange, onCreateSuccess }: CreateGroupProps) {
  const [groupName, setGroupName] = useState('')
  const [groupDescription, setGroupDescription] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [inviteCode, setInviteCode] = useState('')
  const [isPublic, setIsPublic] = useState(true)
  const [shareDialogOpen, setShareDialogOpen] = useState(false)
  const { user } = useSupabaseAuth()
  const addGroup = useChatStore((state) => state.addGroup)
  const { toast } = useToast()

  const handleCreate = async () => {
    if (!user || !groupName.trim()) return

    setIsLoading(true)
    try {
      const code = generateInviteCode()
      const groupId = crypto.randomUUID()
      const newGroupData = {
        id: groupId,
        name: groupName.trim(),
        description: groupDescription.trim() || null,
        members: [user.id],
        createdBy: user.id,
        createdAt: new Date().toISOString(),
        owner_id: user.id,
        inviteCode: code,
        isPublic: isPublic,
        last_activity: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      console.log('Creating group with isPublic:', isPublic)
      const createdGroup = await createGroup(newGroupData)

      // Convert to ChatGroup format for the store
      const chatGroup = {
        id: createdGroup.id,
        name: createdGroup.name,
        description: createdGroup.description || '',
        members: createdGroup.members || [],
        createdAt: new Date(createdGroup.createdAt || '').getTime(),
        ownerId: createdGroup.owner_id || createdGroup.createdBy,
        inviteCode: createdGroup.inviteCode || '',
        isPublic: createdGroup.isPublic || false
      }

      addGroup(chatGroup)
      setInviteCode(code)

      toast({
        title: "Group created!",
        description: `You have created a ${isPublic ? 'public' : 'private'} group.`,
      })

      // Call the success callback if provided
      if (onCreateSuccess) {
        onCreateSuccess(groupId);
        // Close the dialog after successful creation
        handleClose();
      }
    } catch (error) {
      console.error('Error creating group:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to create group. Please try again."
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleInvite = async () => {
    const inviteMessage = `Join my study group on IsotopeAI\n\n` +
      `Group: ${groupName}\n` +
      `Code: ${inviteCode}\n\n` +
      `Join directly: https://isotopeai.in/i/${inviteCode}\n\n` +
      `✨ Let's study together!`

    // Copy to clipboard
    try {
      await navigator.clipboard.writeText(inviteMessage)
      toast({
        title: "Invite copied!",
        description: "The invite message has been copied to your clipboard.",
      })
    } catch (error) {
      console.error('Error copying invite:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to copy invitation message"
      })
    }
    
    // Show sharing options dialog
    setShareDialogOpen(true)
  }
  
  const copyInviteCode = async () => {
    try {
      await navigator.clipboard.writeText(inviteCode)
      toast({
        title: "Copied!",
        description: "Invite code copied to clipboard"
      })
    } catch (error) {
      console.error('Error copying invite code:', error)
    }
  }
  
  const copyInviteLink = async () => {
    try {
      await navigator.clipboard.writeText(`https://isotopeai.in/i/${inviteCode}`)
      toast({
        title: "Copied!",
        description: "Invite link copied to clipboard"
      })
    } catch (error) {
      console.error('Error copying invite link:', error)
    }
  }
  
  const copyFullInvite = async () => {
    try {
      const inviteMessage = `Join my study group on IsotopeAI!\n\n` +
        `Group: ${groupName}\n` +
        `Code: ${inviteCode}\n\n` +
        `Join directly: https://isotopeai.in/i/${inviteCode}\n\n` +
        `✨ Let's study together!`
      
      await navigator.clipboard.writeText(inviteMessage)
      toast({
        title: "Copied!",
        description: "Full invite message copied to clipboard"
      })
    } catch (error) {
      console.error('Error copying full invite message:', error)
    }
  }
  
  const shareViaWhatsApp = () => {
    const whatsappMessage = `Join my study group on IsotopeAI!\n\n` +
      `Group: ${groupName}\n` +
      `Code: ${inviteCode}\n\n` +
      `Join directly: https://isotopeai.in/i/${inviteCode}\n\n` +
      `Let's study together!`
      
    // Create WhatsApp share URL with properly encoded message
    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(whatsappMessage)}`
    
    // Open WhatsApp share in new window
    window.open(whatsappUrl, '_blank')
  }

  const handleClose = () => {
    onOpenChange(false)
    setGroupName('')
    setGroupDescription('')
    setInviteCode('')
    setIsPublic(true)
  }

  return (
    <>
      <Dialog open={open} onOpenChange={handleClose}>
        {/* Theme-aware Dialog Content */}
        <DialogContent className="bg-background border-border text-foreground sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Create New Group</DialogTitle>
          </DialogHeader>
          {!inviteCode ? (
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Group Name</Label>
                {/* Theme-aware Input */}
                <Input
                  id="name"
                  value={groupName}
                  onChange={(e) => setGroupName(e.target.value)}
                  placeholder="Enter group name"
                  className="bg-muted border-border"
                />
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="description">Description (optional)</Label>
                {/* Theme-aware Input */}
                <Input
                  id="description"
                  value={groupDescription}
                  onChange={(e) => setGroupDescription(e.target.value)}
                  placeholder="Brief description of your group"
                  maxLength={100}
                  className="bg-muted border-border"
                />
                <p className="text-xs text-muted-foreground text-right">
                  {groupDescription.length}/100
                </p>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {/* Theme-aware Label */}
                <Label htmlFor="public-toggle" className="flex items-center gap-2 cursor-pointer text-foreground">
                    {isPublic ? (
                      <Globe className="h-4 w-4 text-indigo-400" />
                    ) : (
                      <Lock className="h-4 w-4 text-muted-foreground dark:text-indigo-400" /> // Adjusted private icon color
                    )}
                    <span>{isPublic ? 'Public Group' : 'Private Group'}</span>
                  </Label>
                </div>
                {/* Switch should adapt */}
                <Switch
                  id="public-toggle"
                  checked={isPublic}
                  onCheckedChange={setIsPublic}
                />
              </div>
              
              <div className="text-sm text-muted-foreground">
                {isPublic ? (
                  <p>Public groups can be discovered by anyone. Anyone can join without an invite code.</p>
                ) : (
                  <p>Private groups are hidden. Only people with the invite code can join.</p>
                )}
              </div>
              
              <Button
                onClick={handleCreate}
                disabled={!groupName.trim() || isLoading}
                className="flex items-center justify-center gap-2"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current" />
                    Creating...
                  </>
                ) : (
                  <>Create Group</>
                )}
              </Button>
            </div>
          ) : (
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label>Invite Code</Label>
                <div className="flex gap-2">
                  {/* Theme-aware Input */}
                  <Input value={inviteCode} readOnly className="bg-muted border-border" />
                  {/* Theme-aware Button */}
                  <Button size="icon" variant="outline" onClick={copyInviteCode}>
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
                <p className="text-sm text-muted-foreground">
                  Share this code with others to let them join your group
                </p>
              </div>
              <div className="flex gap-2">
                {/* Theme-aware Button */}
                <Button variant="outline" className="flex-1" onClick={handleInvite}>
                  <Share2 className="h-4 w-4 mr-2" />
                  Share Invite
                </Button>
                {/* Theme-aware Button */}
                <Button variant="outline" onClick={handleClose} className="flex-1">Done</Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
      
      {/* Share Options Dialog */}
      <Dialog open={shareDialogOpen} onOpenChange={setShareDialogOpen}>
        {/* Theme-aware Dialog Content */}
        <DialogContent className="bg-background border-border text-foreground sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Share Group Invite</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label>Invite Link</Label>
              <div className="flex gap-2">
                {/* Theme-aware Input */}
                <Input value={`https://isotopeai.in/i/${inviteCode}`} readOnly className="bg-muted border-border" />
                {/* Theme-aware Button */}
                <Button size="icon" variant="outline" onClick={copyInviteLink}>
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
              <p className="text-sm text-muted-foreground">
                Share this link for one-click joining
              </p>
            </div>
            
            <div className="grid gap-2">
              <Label>Invite Code</Label>
              <div className="flex gap-2">
                {/* Theme-aware Input */}
                <Input value={inviteCode} readOnly className="bg-muted border-border" />
                {/* Theme-aware Button */}
                <Button size="icon" variant="outline" onClick={copyInviteCode}>
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
              <p className="text-sm text-muted-foreground">
                Share this code for manual joining
              </p>
            </div>
            
            {/* Theme-aware info box */}
            <div className="bg-muted p-3 rounded-md border border-border">
              <h4 className="font-medium mb-1 text-foreground">About Invites</h4>
              <p className="text-sm text-muted-foreground">
                Anyone with this link can join your group, even if they don't have an account yet. 
                New users will be guided through the sign-up process before joining.
              </p>
            </div>
            
            <div className="flex justify-end">
              {/* Theme-aware Button */}
              <Button variant="outline" onClick={copyFullInvite}>
                Copy Invite Message
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
